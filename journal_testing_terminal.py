#!/usr/bin/env python
# coding: utf-8

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import xlwings as xw
import numpy as np
import re
import os
import datetime
import openpyxl
from openpyxl.chart import BarChart, Reference
from openpyxl.styles.borders import Border, Side, BORDER_THIN

pd.options.display.float_format = '{:,.2f}'.format

# Global variables
sheet_name = ""
wb = ""
letter = ""
n = 1
num = 1
rev_code, bank_acc, pre_acc, accrual_acc, pl_acc = "", "", "", "", ""
account_codes, doc_no, date, amount, acc_description = "", "", "", "", ""
acc_type, time, post_by, date_format, time_format = '', "", "", "", ""
client_name, client_period, holiday_dates, link = "", "", "", ""
df, dataset = "", ""
monthly_tab, value = "", ""
note_tracker = {}

def create_excel_column_mapping():
    """Create mapping for Excel column letters"""
    mapping = {}
    for i in range(1, 100):
        if i <= 26:
            mapping[i] = chr(64 + i)
        else:
            mapping[i] = f"A{chr(64 + i - 26)}"
    return mapping

def apply_formatting(sheet, data_range, header_range, font_name='Times New Roman'):
    """Apply consistent formatting to Excel sheets"""
    try:
        sheet.range(data_range).api.Borders(3).LineStyle = 1
        sheet.range(data_range).api.Borders(2).LineStyle = 1
        sheet.range(data_range).api.Borders(4).LineStyle = 1
        sheet.range(header_range).api.Borders(3).LineStyle = 1
        sheet.range(header_range).api.Borders(2).LineStyle = 1
        sheet.range(header_range).font.bold = True
        sheet.range(header_range).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
        sheet.range(f"A1:{data_range.split(':')[1]}").font.name = font_name
        sheet.range(f"{header_range.split(':')[0]}:{data_range.split(':')[1]}").columns.autofit()
    except Exception as e:
        print(f"Formatting warning: {e}")

def add_sheet_header(sheet, title):
    """Add standard header to worksheet"""
    sheet["A1"].value = client_name
    sheet.range('A1').api.Font.Bold = True
    sheet["A2"].value = client_period
    sheet.range('A2').api.Font.Bold = True
    sheet["A3"].value = title
    sheet.range('A3').api.Font.Bold = True

def summary_sheet():
    global wb, sheet_name, letter, n, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset, note_tracker
    
    print("Creating summary sheet...")
    
    summary_sheet = wb.sheets.active
    sheet_name = "Summary"
    summary_sheet.name = sheet_name

    # Add borders
    summary_sheet.range("A5:B21").api.Borders(3).LineStyle = 1
    summary_sheet.range("A5:B20").api.Borders(2).LineStyle = 1

    # Header information
    summary_sheet["A1"].value = client_name
    summary_sheet.range('A1').api.Font.Bold = True
    summary_sheet["A2"].value = client_period
    summary_sheet.range('A2').api.Font.Bold = True
    summary_sheet["A3"].value = "Subject: Journal Testing"
    summary_sheet.range('A3').api.Font.Bold = True

    # Column headers
    summary_sheet["A5"].value = "Test"
    summary_sheet.range('A5').api.Font.Bold = True
    summary_sheet['A5'].color = 255, 200, 255
    summary_sheet['A5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    summary_sheet["B5"].value = "Potential exceptions"
    summary_sheet.range('B5').api.Font.Bold = True
    summary_sheet['B5'].color = 255, 200, 255
    summary_sheet['B5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

    # Test descriptions
    tests = [
        "Round entries ,000 or ,999",
        "Date of postings: weekends, bank holidays etc.",
        "Timings of postings - any postings on odd hours",
        "Total amount of transactions per month",
        "Reversed Journal Entries",
        "Gaps/jumps in Journal Entry numbers",
        "Summary of Debit transactions in Revenue codes",
        "Prepayments vs Bank",
        "Accruals vs Bank",
        "Bank accounts vs PnL accounts",
        "Postings by directors on Companies house",
        "Possible duplicate Journal entries",
        "Fraud Word Check",
        "Sales Chronological Testing",
        "Credits in Revenue"
    ]
    
    for i, test in enumerate(tests, start=6):
        summary_sheet[f"A{i}"].value = test

    # Add notes with proper numbering
    note_row = 23
    notes = []
    
    # Check which notes to add based on data availability
    if time == "na" or pd.isna(time_format):
        notes.append(f"Note {len(notes)+1}: No references were provided regarding details of Time posted")
        note_tracker['time'] = len(notes)
    
    if doc_no != "na":
        # Check if doc_no contains non-numeric characters
        try:
            test_df = dataset[dataset[doc_no].notnull()].copy()
            pd.to_numeric(test_df[doc_no], errors='raise')
        except:
            notes.append(f"Note {len(notes)+1}: Impossible to perform Gap test as {doc_no} contains characters like text, slashes and hyphens")
            note_tracker['gap'] = len(notes)
    
    if post_by == "na" or pd.isna(post_by):
        notes.append(f"Note {len(notes)+1}: No references were provided regarding details of users ID and employee key account")
        note_tracker['post_by'] = len(notes)
    
    # Add "None Noted" if no notes
    if not notes:
        notes.append("None Noted")
    
    # Write notes to sheet
    for i, note in enumerate(notes):
        summary_sheet[f"A{note_row + i}"].value = note
        summary_sheet.range(f'A{note_row + i}').api.Font.Bold = True
    
    # Set font for entire range
    summary_sheet.range(f"A1:A{note_row + len(notes)}").font.name = 'Times New Roman'
    wb.sheets[summary_sheet].autofit('c')

def round_entries(Amount):
    global wb, sheet_name, letter, n, num, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset
    
    print("Running Test 1: Round entries...")
    link.append("-")
    
    try:
        if amount != "na":
            entries = dataset[dataset[amount].notnull()].copy()
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",", "")
            entries = entries[entries[amount] != ""]
            
            # Find entries ending with 000 and 999
            entries_000 = entries[entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$")]
            entries_000[amount] = pd.to_numeric(entries_000[amount])
            entries_000 = entries_000.sort_values(amount, ascending=False)
            
            entries_999 = entries[entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$")]
            entries_999[amount] = pd.to_numeric(entries_999[amount])
            entries_999 = entries_999.sort_values(amount, ascending=False)
            
            round_entries = pd.concat([entries_000, entries_999], ignore_index=True)
            
            # Create worksheet
            round_entries_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            link[0] = f"Tab {num}"
            num += 1
            
            add_sheet_header(round_entries_tab, "Round entries ,000 or ,999")
            
            round_entries_tab["A5"].value = "Objective: To find out unusual round number entries in journals."
            round_entries_tab.range('A5').api.Font.Bold = True
            round_entries_tab["A7"].value = 'Method: Filtered all the entries ending with "000" and "999".'
            round_entries_tab.range('A7').api.Font.Bold = True
            
            # Color coding
            round_entries_tab['A9'].color = 255, 200, 255
            round_entries_tab["B9"].value = "Amount ending in '000'"
            round_entries_tab['A10'].color = 221, 235, 247
            round_entries_tab["B10"].value = "Amount ending in '999'"
            
            # Add data and format
            total_rows = 12
            max_cols = 0
            
            if not entries_000.empty:
                r = entries_000.shape[0] + 12
                c = entries_000.shape[1]
                max_cols = max(max_cols, c)
                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"
                
                round_entries_tab.range(data_shape).color = 255, 200, 255
                entries_000 = entries_000.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                round_entries_tab["A12"].options(pd.DataFrame, index=False).value = entries_000
                apply_formatting(round_entries_tab, data_shape, c_shape)
                total_rows = r
            
            if not entries_999.empty:
                r_start = entries_000.shape[0] + 13 if not entries_000.empty else 13
                r_end = r_start + entries_999.shape[0]
                c = entries_999.shape[1]
                max_cols = max(max_cols, c)
                data_shape = f"A{r_start}:{letter[c]}{r_end}"
                
                round_entries_tab.range(data_shape).color = 221, 235, 247
                entries_999 = entries_999.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                round_entries_tab[f"A{r_start}"].options(pd.DataFrame, index=False, header=None).value = entries_999
                apply_formatting(round_entries_tab, data_shape, f"A{r_start}:{letter[c]}{r_start}")
                total_rows = r_end
            
            # Add conclusion
            cell_no = total_rows + 4
            if len(entries_000) > 0 and len(entries_999) > 0:
                conclusion = "Conclusion: Entries ending with '000' & '999' found."
            elif len(entries_000) > 0:
                conclusion = "Conclusion: Entries ending with '000' found."
            elif len(entries_999) > 0:
                conclusion = "Conclusion: Entries ending with '999' found."
            else:
                conclusion = "Conclusion: No Entries ending with '000' & '999' found."
            
            round_entries_tab[f"A{cell_no}"].value = conclusion
            round_entries_tab.range(f'A{cell_no}').api.Font.Bold = True
            round_entries_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
            
            print(f"   Found {len(entries_000)} entries ending with '000' and {len(entries_999)} entries ending with '999'")
            
        else:
            round_entries = 'Col Name Not Given'
        return round_entries
    except Exception as e:
        print(f"Error in round_entries: {e}")
        return "Error processing round entries"

def transactions_per_month(Date, f):
    global wb, sheet_name, letter, n, num, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset, monthly_tab, value

    print("Running Test 4: Transactions per month...")
    link.append("-")
    try:
        if date != "na" and f != "na":
            date = date.strip()
            df = dataset.copy()
            df.sort_values(by=date, inplace=True)

            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]
            df[date] = pd.to_datetime(df[date], format=f)
            df["month"] = df[date].dt.strftime("%m")

            df = df[(df["month"].notnull()) & (df["month"] != "nan")]
            debit_data = df[df[amount] > 0]

            count = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:, 0].values
            sums = pd.pivot_table(debit_data, index="month", values=amount, aggfunc="sum", margins=True).iloc[:, 0].values
            months = pd.pivot_table(df, index="month", values=amount, aggfunc="count", margins=True).iloc[:, 0].index

            count_per = count / sum(count[:-1])
            sums_per = sums / sum(sums[:-1])

            analysis = pd.DataFrame({
                "Month": months,
                "No. of Transactions": count,
                "Value of Transactions": sums,
                'No. of Trans %': count_per,
                'Value. of Trans %': sums_per
            })

            analysis.sort_values(by="Month", inplace=True)
            analysis.reset_index(drop=True, inplace=True)

            import calendar
            l = []
            temp = list(analysis["Month"].values)
            temp = temp[:len(temp) - 1]
            for i in temp:
                m = calendar.month_abbr[int(i)]
                l.append(m)

            l.append("Total")
            analysis["Month"] = l

            transactions_per_month = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            monthly_tab = f"Tab {num}"
            link[3] = f"Tab {num}"
            num += 1

            add_sheet_header(transactions_per_month, "Total Amount of € Transactions Per Month")

            transactions_per_month["A5"].value = "Objective: To find out total no. of transactions and value of total transactions per month."
            transactions_per_month['A5'].font.bold = True
            transactions_per_month["A7"].value = 'Method: Selected per month Debit entries from Journals and noted count and sum of all transaction for each month.'
            transactions_per_month['A7'].font.bold = True

            r = analysis.shape[0] + 9
            c = analysis.shape[1]

            data_shape = f"A10:{letter[c]}{r}"
            c_shape = f"A9:{letter[c]}9"

            analysis = analysis.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            transactions_per_month["A9"].options(pd.DataFrame, index=False).value = analysis
            apply_formatting(transactions_per_month, data_shape, c_shape)
            transactions_per_month.range(f"A{r}:E{r}").font.bold = True
            transactions_per_month.range(f"A{r}:E{r}").color = 221, 235, 247

            value = analysis.iloc[:len(analysis)-1]
            print(f"   Analyzed {len(temp)} months of transaction data")

        else:
            analysis = "Col name is not given"
        return analysis
    except Exception as e:
        print(f"Error in transactions_per_month: {e}")
        return "Error processing transactions per month"

def run_journal_testing():
    global wb, sheet_name, letter, n, num, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset, monthly_tab, value, note_tracker

    print("=" * 60)
    print("JOURNAL TESTING - TERMINAL VERSION")
    print("=" * 60)

    # Get input file name
    sheet_name = input("Enter the data file name (Excel/CSV): ").strip()

    if not sheet_name:
        print("❌ Error: File name cannot be empty!")
        return

    if not os.path.exists(sheet_name):
        print(f"❌ Error: File '{sheet_name}' not found!")
        return

    try:
        print(f"\n📊 Loading data from: {sheet_name}")

        # Initialize variables
        wb = xw.Book()
        letter = create_excel_column_mapping()
        num = 1
        link = []
        note_tracker = {}

        # Load data
        try:
            df = pd.read_excel(sheet_name)
            print(f"✅ Loaded Excel file with {len(df)} rows")
        except:
            df = pd.read_csv(sheet_name)
            print(f"✅ Loaded CSV file with {len(df)} rows")

        dataset = df.copy()

        # Load configuration data
        config_file = "SOP for Data Analyst.xlsx"

        if not os.path.exists(config_file):
            print(f"❌ Error: Configuration file '{config_file}' not found!")
            return

        print("📋 Loading configuration...")

        rev_code = list(pd.read_excel(config_file, sheet_name="Revenue Code")["Revenue Code"].astype(str))
        bank_acc = list(pd.read_excel(config_file, sheet_name="Bank Code")["Bank Code"].astype(str))
        pre_acc = list(pd.read_excel(config_file, sheet_name="Prepayment Code")["Prepayment Code"].astype(str))
        accrual_acc = list(pd.read_excel(config_file, sheet_name="Accrual Code")["Accrual Code"].astype(str))
        pl_acc = list(pd.read_excel(config_file, sheet_name="PL Code")["PL Code"].astype(str))

        # Load column mappings
        col_name = pd.read_excel(config_file, sheet_name="Col Name").iloc[:, 1]
        account_codes, doc_no, date, amount, acc_description = col_name[0:5]
        acc_type, time, post_by, date_format, time_format = col_name[5:10]

        # Load client details
        client_detail = pd.read_excel(config_file, sheet_name="Journal Testing", header=None)
        client_name = client_detail.iloc[1, 0]
        client_period = f"Period : {client_detail.iloc[8, 1].strftime('%B %Y')} To {client_detail.iloc[8, 2].strftime('%B %Y')}"

        # Load holiday dates
        holiday_detail = pd.read_excel(config_file, sheet_name="Journal Testing", header=None)
        date_index = holiday_detail[holiday_detail[0].str.strip() == "Date"].index

        holiday_dates = []
        if len(date_index) > 0:
            for date_val in holiday_detail.iloc[date_index[0]+1:, 0].values:
                if pd.isna(date_val):
                    break
                holiday_dates.append(date_val.strftime("%Y-%m-%d"))

        print(f"✅ Configuration loaded for client: {client_name}")
        print(f"📅 Period: {client_period}")

        print("\n🔍 Starting Journal Testing Analysis...")
        print("-" * 40)

        # Create summary sheet
        summary_sheet()

        # Run core tests
        monthly_tab = ""
        value = ""

        round_entries(amount)
        transactions_per_month(date, date_format)

        print("\n💾 Saving results...")

        # Extract client name and create proper filename
        client_parts = client_name.split(":")
        if len(client_parts) > 1:
            clean_client_name = client_parts[1].strip()
        else:
            clean_client_name = client_name.strip()

        # Remove invalid filename characters
        clean_client_name = re.sub(r'[<>:"/\\|?*]', '', clean_client_name)
        final_filename = f"{clean_client_name} - Journal Testing"

        file_name = wb.name
        wb.save()
        wb.close()

        # Create charts if monthly data exists
        if monthly_tab and value is not None and not value.empty:
            print("📊 Creating charts...")
            l = len(value.iloc[:, 0])
            wb_openpyxl = openpyxl.load_workbook(f'{file_name}.xlsx')
            sheet = wb_openpyxl[monthly_tab]

            # Chart 1: Transaction percentages comparison
            values = Reference(sheet, min_col=4, max_col=4, min_row=9, max_row=9+l)
            values1 = Reference(sheet, min_col=5, max_col=5, min_row=9, max_row=9+l)
            cats = Reference(sheet, min_col=1, max_col=1, min_row=10, max_row=10+l)

            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.add_data(values1, titles_from_data=True)
            chart.set_categories(cats)
            chart.title = "No. of Trans% VS Value of Trans%"
            sheet.add_chart(chart, "G9")

            # Chart 2: Number of transactions
            values = Reference(sheet, min_col=2, max_col=2, min_row=9, max_row=9+l)
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            chart.title = "No. Of Transaction"
            sheet.add_chart(chart, "P9")

            # Chart 3: Value of transactions
            values = Reference(sheet, min_col=3, max_col=3, min_row=9, max_row=9+l)
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            chart.title = "Value Of Transaction"
            sheet.add_chart(chart, "Y9")

            # Add hyperlinks to summary sheet
            thin_border = Border(
                left=Side(border_style=BORDER_THIN, color='00000000'),
                right=Side(border_style=BORDER_THIN, color='00000000'),
                top=Side(border_style=BORDER_THIN, color='00000000'),
                bottom=Side(border_style=BORDER_THIN, color='00000000')
            )

            sheet = wb_openpyxl["Summary"]
            n = 5
            for i in link:
                n += 1
                if i != "-":
                    sheet[f"B{n}"] = f'=HYPERLINK("#\'{i}\'!A1","{i}")'
                    sheet[f"B{n}"].style = "Hyperlink"
                    sheet.cell(row=n, column=2).border = thin_border

            wb_openpyxl.save(f"{final_filename}.xlsx")
            wb_openpyxl.close()
        else:
            # Just rename the file if no charts needed
            wb_openpyxl = openpyxl.load_workbook(f'{file_name}.xlsx')
            wb_openpyxl.save(f"{final_filename}.xlsx")
            wb_openpyxl.close()

        # Remove temporary file if it exists
        if os.path.exists(f'{file_name}.xlsx') and file_name != final_filename:
            os.remove(f'{file_name}.xlsx')

        print(f"✅ Analysis complete!")
        print(f"📁 File saved as: {final_filename}.xlsx")
        print(f"📊 Generated {len([x for x in link if x != '-'])} analysis tabs")

    except Exception as e:
        # Clean up any temporary files
        for i in os.listdir():
            if i.startswith("Book"):
                try:
                    os.remove(i)
                except:
                    pass
        print(f"❌ Error: {e}")
        print("Something went wrong during analysis")

if __name__ == "__main__":
    run_journal_testing()
