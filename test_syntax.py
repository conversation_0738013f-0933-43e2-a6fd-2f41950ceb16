#!/usr/bin/env python3
"""
Simple syntax test for the optimized journal testing code
"""

import ast
import sys

def test_syntax(filename):
    """Test if the Python file has valid syntax"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            source = f.read()
        
        # Parse the source code
        ast.parse(source)
        print(f"✅ {filename} - Syntax is valid!")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} - Syntax Error:")
        print(f"   Line {e.lineno}: {e.text}")
        print(f"   Error: {e.msg}")
        return False
        
    except Exception as e:
        print(f"❌ {filename} - Error: {e}")
        return False

if __name__ == "__main__":
    # Test both files
    files_to_test = ["file.py", "journal_testing_optimized.py"]
    
    all_valid = True
    for filename in files_to_test:
        try:
            valid = test_syntax(filename)
            all_valid = all_valid and valid
        except FileNotFoundError:
            print(f"⚠️  {filename} - File not found, skipping...")
    
    if all_valid:
        print("\n🎉 All files have valid syntax!")
        sys.exit(0)
    else:
        print("\n💥 Some files have syntax errors!")
        sys.exit(1)
