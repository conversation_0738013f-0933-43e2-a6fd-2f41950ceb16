#!/usr/bin/env python
# coding: utf-8

import warnings
warnings.filterwarnings('ignore')

import pandas as pd
import xlwings as xw
import numpy as np
import re
import os
import datetime
import openpyxl
from openpyxl.chart import BarChart, Reference
from openpyxl.styles.borders import Border, Side, BORDER_THIN

from tkinter import *
from tkinter.ttk import *
from tkinter import messagebox

pd.options.display.float_format = '{:,.2f}'.format



# In[2]:


def summary_sheet():
    global wb, sheet_name, letter, n, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset, note_tracker

    summary_sheet = wb.sheets.active
    sheet_name = "Summary"
    summary_sheet.name = sheet_name

    # Add borders
    summary_sheet.range("A5:B21").api.Borders(3).LineStyle = 1
    summary_sheet.range("A5:B20").api.Borders(2).LineStyle = 1

    # Header information
    summary_sheet["A1"].value = client_name
    summary_sheet.range('A1').api.Font.Bold = True
    summary_sheet["A2"].value = client_period
    summary_sheet.range('A2').api.Font.Bold = True
    summary_sheet["A3"].value = "Subject: Journal Testing"
    summary_sheet.range('A3').api.Font.Bold = True

    # Column headers
    summary_sheet["A5"].value = "Test"
    summary_sheet.range('A5').api.Font.Bold = True
    summary_sheet['A5'].color = 255, 200, 255
    summary_sheet['A5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    summary_sheet["B5"].value = "Potential exceptions"
    summary_sheet.range('B5').api.Font.Bold = True
    summary_sheet['B5'].color = 255, 200, 255
    summary_sheet['B5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

    # Test descriptions
    tests = [
        "Round entries ,000 or ,999",
        "Date of postings: weekends, bank holidays etc.",
        "Timings of postings - any postings on odd hours",
        "Total amount of transactions per month",
        "Reversed Journal Entries",
        "Gaps/jumps in Journal Entry numbers",
        "Summary of Debit transactions in Revenue codes",
        "Prepayments vs Bank",
        "Accruals vs Bank",
        "Bank accounts vs PnL accounts",
        "Postings by directors on Companies house",
        "Possible duplicate Journal entries",
        "Fraud Word Check",
        "Sales Chronological Testing",
        "Credits in Revenue"
    ]

    for i, test in enumerate(tests, start=6):
        summary_sheet[f"A{i}"].value = test

    # Add notes with proper numbering
    note_row = 23
    notes = []

    # Check which notes to add based on data availability
    if time == "na" or pd.isna(time_format):
        notes.append(f"Note {len(notes)+1}: No references were provided regarding details of Time posted")
        note_tracker['time'] = len(notes)

    if doc_no != "na":
        # Check if doc_no contains non-numeric characters
        try:
            test_df = dataset[dataset[doc_no].notnull()].copy()
            pd.to_numeric(test_df[doc_no], errors='raise')
        except:
            notes.append(f"Note {len(notes)+1}: Impossible to perform Gap test as {doc_no} contains characters like text, slashes and hyphens")
            note_tracker['gap'] = len(notes)

    if post_by == "na" or pd.isna(post_by):
        notes.append(f"Note {len(notes)+1}: No references were provided regarding details of users ID and employee key account")
        note_tracker['post_by'] = len(notes)

    # Add "None Noted" if no notes
    if not notes:
        notes.append("None Noted")

    # Write notes to sheet
    for i, note in enumerate(notes):
        summary_sheet[f"A{note_row + i}"].value = note
        summary_sheet.range(f'A{note_row + i}').api.Font.Bold = True

    # Set font for entire range
    summary_sheet.range(f"A1:A{note_row + len(notes)}").font.name = 'Times New Roman'
    wb.sheets[summary_sheet].autofit('c')
    


# ### TEST 1

# In[3]:


def round_entries(Amount):
    global wb, sheet_name, letter, n, num, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset

    link.append("-")

    try:
        if amount != "na":
            entries = dataset[dataset[amount].notnull()].copy()
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",", "")
            entries = entries[entries[amount] != ""]

            # Find entries ending with 000 and 999
            entries_000 = entries[entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$")]
            entries_000[amount] = pd.to_numeric(entries_000[amount])
            entries_000 = entries_000.sort_values(amount, ascending=False)

            entries_999 = entries[entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$")]
            entries_999[amount] = pd.to_numeric(entries_999[amount])
            entries_999 = entries_999.sort_values(amount, ascending=False)

            round_entries = pd.concat([entries_000, entries_999], ignore_index=True)

            # Create worksheet
            round_entries_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            link[0] = f"Tab {num}"
            num += 1

            # Add headers
            round_entries_tab["A1"].value = client_name
            round_entries_tab.range('A1').api.Font.Bold = True
            round_entries_tab["A2"].value = client_period
            round_entries_tab.range('A2').api.Font.Bold = True
            round_entries_tab["A3"].value = "Round entries ,000 or ,999"
            round_entries_tab.range('A3').api.Font.Bold = True

            round_entries_tab["A5"].value = "Objective: To find out unusual round number entries in journals."
            round_entries_tab.range('A5').api.Font.Bold = True
            round_entries_tab["A7"].value = 'Method: Filtered all the entries ending with "000" and "999".'
            round_entries_tab.range('A7').api.Font.Bold = True

            # Color coding
            round_entries_tab['A9'].color = 255, 200, 255
            round_entries_tab["B9"].value = "Amount ending in '000'"
            round_entries_tab['A10'].color = 221, 235, 247
            round_entries_tab["B10"].value = "Amount ending in '999'"

            # Add 000 entries
            if not entries_000.empty:
                r = entries_000.shape[0] + 12
                c = entries_000.shape[1]
                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                round_entries_tab.range(data_shape).color = 255, 200, 255
                entries_000 = entries_000.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                round_entries_tab["A12"].options(pd.DataFrame, index=False).value = entries_000
                round_entries_tab.range(c_shape).font.bold = True
                round_entries_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

                # Add borders
                for border_range in [data_shape, c_shape]:
                    round_entries_tab.range(border_range).api.Borders(3).LineStyle = 1
                    round_entries_tab.range(border_range).api.Borders(2).LineStyle = 1

            # Add 999 entries
            if not entries_999.empty:
                r_start = entries_000.shape[0] + 13 if not entries_000.empty else 13
                r_end = r_start + entries_999.shape[0]
                c = entries_999.shape[1]
                data_shape = f"A{r_start}:{letter[c]}{r_end}"

                round_entries_tab.range(data_shape).color = 221, 235, 247
                entries_999 = entries_999.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                round_entries_tab[f"A{r_start}"].options(pd.DataFrame, index=False, header=None).value = entries_999

                # Add borders
                round_entries_tab.range(data_shape).api.Borders(3).LineStyle = 1
                round_entries_tab.range(data_shape).api.Borders(2).LineStyle = 1
                round_entries_tab.range(data_shape).api.Borders(4).LineStyle = 1

            # Format and add conclusion
            total_rows = entries_000.shape[0] + entries_999.shape[0] + 12
            round_entries_tab.range(f"A1:{letter[max(entries_000.shape[1], entries_999.shape[1])]}{total_rows}").font.name = 'Times New Roman'
            round_entries_tab.range(f"A12:{letter[max(entries_000.shape[1], entries_999.shape[1])]}{total_rows}").columns.autofit()

            cell_no = total_rows + 4

            # Add conclusion based on findings
            if len(entries_000) > 0 and len(entries_999) > 0:
                conclusion = "Conclusion: Entries ending with '000' & '999' found."
            elif len(entries_000) > 0:
                conclusion = "Conclusion: Entries ending with '000' found."
            elif len(entries_999) > 0:
                conclusion = "Conclusion: Entries ending with '999' found."
            else:
                conclusion = "Conclusion: No Entries ending with '000' & '999' found."

            round_entries_tab[f"A{cell_no}"].value = conclusion
            round_entries_tab.range(f'A{cell_no}').api.Font.Bold = True
            round_entries_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

        else:
            round_entries = 'Col Name Not Given'
        return round_entries
    except Exception as e:
        print(f"Error in round_entries: {e}")
        return "Error processing round entries"


# ### TEST 2

# In[4]:


def holidaysandweekend(Date , f):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    
    try:
        if date != "na" and f!= "na":
            date = date.strip()
            df = dataset.copy()
            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]
            df[date] = pd.to_datetime(df[date], format = f)
            holidays = holiday_dates
            holiday = df[df[date].isin(holidays)]
            weekend_1 = df[df[date].dt.strftime("%A") == "Saturday"]
            weekend_2 = df[df[date].dt.strftime("%A") == "Sunday"]
            holiday["Holiday"] = "Holiday"
            weekend_1["Holiday"] = "Saturday"
            weekend_2["Holiday"] = "Sunday"
            holiday = holiday.sort_values(amount, ascending=False)
            weekend_1 = weekend_1.sort_values(amount, ascending=False)
            weekend_2 = weekend_2.sort_values(amount, ascending=False)
            weekend = pd.concat([weekend_1,weekend_2], ignore_index=True)
            holidays_trans = pd.concat([holiday,weekend_1,weekend_2], ignore_index=True)
            holidays_trans = holidays_trans.drop_duplicates()
            holiday = holidays_trans[holidays_trans["Holiday"] == "Holiday"]
            weekend_1 = holidays_trans[holidays_trans["Holiday"] == "Saturday"]
            weekend_2 = holidays_trans[holidays_trans["Holiday"] == "Sunday"]

            
            
            holidays_trans_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[1] = f"Tab {num}"
            num += 1
            holidays_trans_tab["A1"].value = client_name
            holidays_trans_tab['A1'].font.bold = True
            holidays_trans_tab["A2"].value = client_period
            holidays_trans_tab['A2'].font.bold = True
            holidays_trans_tab["A3"].value = "Date of postings: weekends, bank holidays etc."
            holidays_trans_tab['A3'].font.bold = True
            
            
            holidays_trans_tab["A5"].value = "Objective: To find out unusual journals entered on holidays and weekends."
            holidays_trans_tab['A5'].font.bold = True
            
            holidays_trans_tab["A7"].value = 'Method: Filtered all the entries posted on holidays and on weekends.'
            holidays_trans_tab['A7'].font.bold = True
           
            
            holidays_trans_tab['A9'].color = 255 , 200 , 255
            holidays_trans_tab["B9"].value = 'Posting on "Weekend"'
            holidays_trans_tab['A10'].color = 221 , 235 , 247
            holidays_trans_tab["B10"].value = 'Posting on "Holiday"'
            
            
            r = weekend.shape[0] + 12
            c = weekend.shape[1]
            
            data_shape = f"A13:{letter[c]}{r}"
            c_shape = f"A12:{letter[c]}12"
            
            holidays_trans_tab.range(data_shape).color = 255 , 200 , 255
            import datetime
            weekend = weekend.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            holidays_trans_tab["A12"].options(pd.DataFrame, index=False).value = weekend
            holidays_trans_tab.range(c_shape).font.bold = True
            holidays_trans_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            holidays_trans_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            holidays_trans_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            holidays_trans_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            holidays_trans_tab.range(c_shape).api.Borders(2).LineStyle =  1 
            
            
            
            r = weekend.shape[0] + 13
            c = holiday.shape[1]
            
            data_shape = f"A{r}:{letter[c]}{weekend.shape[0] + holiday.shape[0] + 13}"
            c_shape = f"A{r}:{letter[c]}{c}"
            
            holidays_trans_tab.range(data_shape).color = 221 , 235 , 247
            import datetime
            holiday = holiday.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            holidays_trans_tab[f"A{r}"].options(pd.DataFrame, index=False, header = None).value = holiday
#             round_entries_tab.range(c_shape).font.bold = True
#             holidays_trans_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            holidays_trans_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            holidays_trans_tab.range(data_shape).api.Borders(2).LineStyle =  1
            holidays_trans_tab.range(data_shape).api.Borders(4).LineStyle =  1
#             holidays_trans_tab.range(c_shape).api.Borders(3).LineStyle =  1 
#             holidays_trans_tab.range(c_shape).api.Borders(2).LineStyle =  1
        
            holidays_trans_tab.range(f"A1:{letter[c]}{weekend.shape[0] + holiday.shape[0] + 12}").font.name = 'Times New Roman'
            holidays_trans_tab.range(f"A12:{letter[c]}{weekend.shape[0] + holiday.shape[0] + 12}").columns.autofit()
            
            
            cell_no = weekend.shape[0] + holiday.shape[0] + 16
            
            if len(weekend) > 0 and len(holiday) > 0:
                holidays_trans_tab[f"A{cell_no}"].value = "Conclusion: Entries posted in 'Weekend' & 'Holiday' found."
                holidays_trans_tab.range(f'A{cell_no}').api.Font.Bold = True
                holidays_trans_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
                
            elif len(weekend) > 0 and len(holiday) <= 0:
                holidays_trans_tab[f"A{cell_no}"].value = "Conclusion: Entries posted in 'Weekend' found."
                holidays_trans_tab.range(f'A{cell_no}').api.Font.Bold = True
                holidays_trans_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
                
            elif len(weekend) <= 0 and len(holiday) > 0:
                holidays_trans_tab[f"A{cell_no}"].value = "Conclusion: Entries posted in 'Holiday' found."
                holidays_trans_tab.range(f'A{cell_no}').api.Font.Bold = True
                holidays_trans_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
                
            else:
                holidays_trans_tab[f"A{cell_no}"].value = "Conclusion: No Entries posted in 'Weekend' & 'Holiday' found."
                holidays_trans_tab.range(f'A{cell_no}').api.Font.Bold = True
                holidays_trans_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
            
        else:
            holidays_trans = 'Col Name Not Given'
        
        return holidays_trans
    except Exception as e:
        print(e)
        print("Something else went wrong")
    


# ### TEST 3

# In[5]:


def odd_hours_entries(Time , f):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    
    try:
        if time != "na" and f!= "na":
            time = time.strip()
            df = dataset.copy()
            df[time] = df[time].astype(str).str.strip()
            df = df[df[time].notnull()]
            df = df[df[time] != ""]
            df[time] = pd.to_datetime(df[time], format = f)
#             pm_7 = df[df[time].dt.hour == 19]
            pm_8 = df[df[time].dt.hour == 20]
            pm_9 = df[df[time].dt.hour == 21]
            pm_10 = df[df[time].dt.hour == 22]
            pm_11 = df[df[time].dt.hour == 23]
            am_12 = df[df[time].dt.hour == 0]
            am_1 = df[df[time].dt.hour == 1]
            am_2 = df[df[time].dt.hour == 2]
            am_3 = df[df[time].dt.hour == 3]
            am_4 = df[df[time].dt.hour == 4]
            am_5 = df[df[time].dt.hour == 5]
            am_6 = df[df[time].dt.hour == 6]
            am_7 = df[df[time].dt.hour == 7]
            am_8 = df[df[time].dt.hour == 8]
            
#             pm_7["Hours"] = "7 PM" 
            pm_8["Hours"] = "8 PM"
            pm_9["Hours"] = "9 PM"
            pm_10["Hours"] = "10 PM"
            pm_11["Hours"] = "11 PM"
            am_12["Hours"] = "12 AM"
            am_1["Hours"] = "1 AM"
            am_2["Hours"] = "2 AM"
            am_3["Hours"] = "3 AM"
            am_4["Hours"] = "4 AM"
            am_5["Hours"] = "5 AM"
            am_6["Hours"] = "6 AM"
            am_7["Hours"] = "7 AM"
            am_8["Hours"] = "8 AM"
            
            odd_hours_am = pd.concat([am_12,am_1,am_2,am_3,am_4,am_5,am_6,am_7,am_8], ignore_index=True)
            odd_hours_pm = pd.concat([pm_8,pm_9,pm_10,pm_11], ignore_index=True)
            odd_hours_am = odd_hours_am.sort_values(amount, ascending=False)
            odd_hours_pm = odd_hours_pm.sort_values(amount, ascending=False)
            
            
            odd_hours_concat = pd.concat([pm_8,pm_9,pm_10,pm_11,am_12,am_1,am_2,am_3,am_4,am_5,am_6,am_7,am_8], ignore_index=True)
            
            
            if odd_hours_concat.shape[0] <= 1000000:
            
                odd_hours_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
                link[2] = f"Tab {num}"
                num += 1
                odd_hours_tab["A1"].value = client_name
                odd_hours_tab['A1'].font.bold = True
                odd_hours_tab["A2"].value = client_period
                odd_hours_tab['A2'].font.bold = True
                odd_hours_tab["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab['A3'].font.bold = True


                odd_hours_tab["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab['A5'].font.bold = True

                odd_hours_tab["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab['A7'].font.bold = True


                odd_hours_tab['A9'].color = 255 , 200 , 255
                odd_hours_tab["B9"].value = 'Posting on "AM"'
                odd_hours_tab['A10'].color = 221 , 235 , 247
                odd_hours_tab["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab.range(data_shape).color = 255 , 200 , 255
                import datetime
                odd_hours_am = odd_hours_am.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab["A12"].options(pd.DataFrame, index=False).value = odd_hours_am
                odd_hours_tab.range(c_shape).font.bold = True
                odd_hours_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(data_shape).api.Borders(2).LineStyle =  1
                odd_hours_tab.range(data_shape).api.Borders(4).LineStyle =  1
                odd_hours_tab.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(c_shape).api.Borders(2).LineStyle =  1 

                r = odd_hours_am.shape[0] + 13
                c = odd_hours_pm.shape[1]

                data_shape = f"A{r}:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 13}"
                c_shape = f"A{r}:{letter[c]}{c}"

                odd_hours_tab.range(data_shape).color = 221 , 235 , 247
                import datetime
                odd_hours_pm = odd_hours_pm.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab[f"A{r}"].options(pd.DataFrame, index=False, header = None).value = odd_hours_pm
                odd_hours_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(data_shape).api.Borders(2).LineStyle =  1 
                
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()
                
                cell_no = odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 16
            
                if len(odd_hours_am) > 0 or len(odd_hours_pm) > 0:
                    odd_hours_tab[f"A{cell_no}"].value = "Conclusion: Entries posted in 'Odd hours' found."
                    odd_hours_tab.range(f'A{cell_no}').api.Font.Bold = True
                    odd_hours_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


                else:
                    odd_hours_tab[f"A{cell_no}"].value = "Conclusion: No Entries posted in 'Odd hours' found."
                    odd_hours_tab.range(f'A{cell_no}').api.Font.Bold = True
                    odd_hours_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
                    
                    
            else:
                odd_hours_tab1 = wb.sheets.add("Tab 3.1", after = wb.sheets.active)
                odd_hours_tab1["A1"].value = client_name
                odd_hours_tab1['A1'].font.bold = True
                odd_hours_tab1["A2"].value = client_period
                odd_hours_tab1['A2'].font.bold = True
                odd_hours_tab1["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab1['A3'].font.bold = True


                odd_hours_tab1["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab1['A5'].font.bold = True

                odd_hours_tab1["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab1['A7'].font.bold = True


                odd_hours_tab1['A9'].color = 255 , 200 , 255
                odd_hours_tab1["B9"].value = 'Posting on "AM"'
                odd_hours_tab1['A10'].color = 221 , 235 , 247
                odd_hours_tab1["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab1.range(data_shape).color = 255 , 200 , 255
                import datetime
                odd_hours_am = odd_hours_am.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab1["A12"].options(pd.DataFrame, index=False).value = odd_hours_am
                odd_hours_tab1.range(c_shape).font.bold = True
                odd_hours_tab1.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab1.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab1.range(data_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab1.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab1.range(c_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()

                odd_hours_tab2 = wb.sheets.add("Tab 3.2", after = wb.sheets.active)
                odd_hours_tab2["A1"].value = client_name
                odd_hours_tab2['A1'].font.bold = True
                odd_hours_tab2["A2"].value = client_period
                odd_hours_tab2['A2'].font.bold = True
                odd_hours_tab2["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab2['A3'].font.bold = True


                odd_hours_tab2["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab2['A5'].font.bold = True

                odd_hours_tab2["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab2['A7'].font.bold = True


                odd_hours_tab2['A9'].color = 255 , 200 , 255
                odd_hours_tab2["B9"].value = 'Posting on "AM"'
                odd_hours_tab2['A10'].color = 221 , 235 , 247
                odd_hours_tab2["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab2.range(data_shape).color = 221 , 235 , 247
                import datetime
                odd_hours_pm = odd_hours_pm.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab2["A12"].options(pd.DataFrame, index=False).value = odd_hours_pm
                odd_hours_tab2.range(c_shape).font.bold = True
                odd_hours_tab2.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab2.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab2.range(data_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab2.range(data_shape).api.Borders(4).LineStyle =  1 
                odd_hours_tab2.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab2.range(c_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()

        else:
            odd_hours_concat = 'Col Name Not Given'
        return odd_hours_concat
    except Exception as e:
        print(e)
        print("Something else went wrong")
    


# ### TEST 4

# In[6]:


monthly_tab,value = "",""


# In[7]:


def transactions_per_month(Date, f):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    
    global monthly_tab
    global value
    
    link.append("-")
    try:
        if date != "na" and f!="na":
            
            date = date.strip()        
            df = dataset.copy()
            df.sort_values(by = date,inplace = True)
            
            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]
            df[date] = pd.to_datetime(df[date], format = f)
            df["month"] = df[date].dt.strftime("%m")

            df = df[(df["month"].notnull()) & (df["month"] != "nan")]
            unique_month = df["month"].unique()
            debit_data = df[df[amount] > 0]
            
            #m =  pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].index
            count = pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].values
            sums = pd.pivot_table(debit_data,index = "month",values = amount,aggfunc ="sum",margins = True).iloc[:,0].values
            months =  pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].index
            
            count_per = count / sum(count[:-1]) 
            sums_per = sums / sum(sums[:-1]) 

            analysis = pd.DataFrame({"Month":months,"No. of Transactions":count,"Value of Transactions":sums,'No. of Trans %':count_per,\
                                    'Value. of Trans %':sums_per})
            #analysis.columns = ["No of Transactions","Value of Transactions"]
            #analysis.insert(0,"Month",analysis.index)

            analysis.sort_values(by = "Month",inplace = True)
            analysis.reset_index(drop = True,inplace = True)

            import calendar
            
            l = []
            temp = list(analysis["Month"].values)
            temp = temp[:len(temp) - 1]
            for i in temp:
                m = calendar.month_abbr[int(i)]
                l.append(m)
            
            l.append("Total")
            analysis["Month"] = l
            
            
#############

            transactions_per_month = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            monthly_tab = f"Tab {num}"
            link[3] = f"Tab {num}"
            num += 1
            transactions_per_month["A1"].value = client_name
            transactions_per_month['A1'].font.bold = True
            transactions_per_month["A2"].value = client_period
            transactions_per_month['A2'].font.bold = True
            transactions_per_month["A3"].value = "Total Amount of € Transactions Per Month."
            transactions_per_month['A3'].font.bold = True
            
            
            transactions_per_month["A5"].value = "Objective: To find out total no. of transactions and value of total transactions per month."
            transactions_per_month['A5'].font.bold = True
            
            transactions_per_month["A7"].value = 'Method: Selected per month Debit entries from Journals and noted count and sum of all transaction for each month.'
            transactions_per_month['A7'].font.bold = True
            
            
            r = analysis.shape[0] + 9
            c = analysis.shape[1]
            
            data_shape = f"A10:{letter[c]}{r}"
            c_shape = f"A9:{letter[c]}9"
            
            import datetime
            analysis = analysis.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            transactions_per_month["A9"].options(pd.DataFrame, index=False).value = analysis
            transactions_per_month.range(c_shape).font.bold = True
            transactions_per_month.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            transactions_per_month.range(c_shape).columns.autofit()
            transactions_per_month.range(f"A10:{letter[c]}{r+1}").api.Borders(3).LineStyle =  1 
            transactions_per_month.range(data_shape).api.Borders(2).LineStyle =  1 
            transactions_per_month.range(c_shape).api.Borders(3).LineStyle =  1 
            transactions_per_month.range(c_shape).api.Borders(2).LineStyle =  1
            transactions_per_month.range(f"A{r}:E{r}").font.bold = True
            transactions_per_month.range(f"A{r}:E{r}").color = 221 , 235 , 247
            
            value = analysis.iloc[:len(analysis)-1]
            
            transactions_per_month.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            transactions_per_month.range(f"A9:{letter[c]}{r}").columns.autofit()
            ####
#             fig = plt.figure(figsize = (6,3.5))
#             ax = fig.add_axes([0,0,1,1])
#             x = "Month"
#             y = "No of Transactions"
#             #plt.xticks(rotation = 45)
#             ax.ticklabel_format(style='plain', axis='y')
#             plt.title(f"{y}",fontsize=20,fontweight = 20)
#             plt.tight_layout()
#             chart1 = ax.bar(data = analysis.iloc[:len(analysis)-1,:],x = x, height = y,color="grey",width = 0.4)
#             plt.xticks(fontsize=13)
#             plt.yticks(fontsize=12)
#             transactions_per_month.pictures.add(fig, name='No Of Transactions', update=True,left = transactions_per_month.range('E9').left,\
#                                                top = transactions_per_month.range('E9').top)

            
#             ####
#             fig = plt.figure(figsize = (6,3.5))
#             ax = fig.add_axes([0,0,1,1])
#             x = "Month"
#             y = "Value of Transactions"
#             #plt.xticks(rotation = 45)
#             ax.ticklabel_format(style='plain', axis='y')
#             plt.title(f"{y}",fontsize=20,fontweight = 20)
#             plt.tight_layout()
#             chart2 = ax.bar(data = analysis.iloc[:len(analysis)-1,:],x = x, height = y,color="grey",width = 0.4)
                
#             plt.xticks(fontsize=13)
#             plt.yticks(fontsize=12)
#             transactions_per_month.pictures.add(fig, name='Value Of Transactions', update=True,left = transactions_per_month.range('N9').left,\
#                                                top = transactions_per_month.range('O9').top)

    
        else:
            analysis = "Col name is not given"
        return analysis
    except Exception as e:
        print(e)
        print("Something else went wrong") 


# ### TEST 5

# In[8]:


def reversed_entries(Acc_description, pttrn = ["reversal","reverse","reversl","reversing"]):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if acc_description != "na":
        acc_description = acc_description.strip()        
        df = dataset.copy()
        df[acc_description] = df[acc_description].astype(str).str.strip()
        import re
        reversal_entries = pd.DataFrame()
        for i in pttrn :
            entries = df[df[acc_description].str.contains(i,flags = re.I) == True]
            reversal_entries = pd.concat([reversal_entries, entries], ignore_index = False)
            reversal_entries = reversal_entries.sort_values(amount, ascending=False)
        
        if len(reversal_entries) > 0:
            reversal_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[4] = f"Tab {num}"
            num += 1
            reversal_tab["A1"].value = client_name
            reversal_tab['A1'].font.bold = True
            reversal_tab["A2"].value = client_period
            reversal_tab['A2'].font.bold = True
            reversal_tab["A3"].value = "Reversed Journal Entries."
            reversal_tab['A3'].font.bold = True


            reversal_tab["A5"].value = "Objective: to identify reversal transaction in journal dump."
            reversal_tab['A5'].font.bold = True

            reversal_tab["A7"].value = 'Method: Selected all reversal transaction posted in the ledger.'
            reversal_tab['A7'].font.bold = True


            r = reversal_entries.shape[0] + 10
            c = reversal_entries.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            reversal_entries = reversal_entries.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            reversal_tab["A10"].options(pd.DataFrame, index=False).value = reversal_entries
            reversal_tab.range(c_shape).font.bold = True
            reversal_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            reversal_tab.range(c_shape).columns.autofit()
            reversal_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            reversal_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            reversal_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            reversal_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            reversal_tab.range(c_shape).api.Borders(2).LineStyle =  1 
            
            reversal_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            reversal_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            
            cell_no = r + 4
            
            if len(reversal_entries) > 0:
                reversal_tab[f"A{cell_no}"].value = "Conclusion: Reversal Transactions found."
                reversal_tab.range(f'A{cell_no}').api.Font.Bold = True
                reversal_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


            else:
                reversal_tab[f"A{cell_no}"].value = "Conclusion: No Reversal Transactions found."
                reversal_tab.range(f'A{cell_no}').api.Font.Bold = True
                reversal_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
        
        else:
            reversal_entries = "Reversal entries not found"

    else:
        reversal_entries = "Col name is not given"
    return reversal_entries


# ### TEST 6

# In[9]:


def gaps(Doc_no):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if doc_no != "na":
        try:
            doc_no = doc_no.strip()        
            df = dataset.copy()
            df = df[df[doc_no].notnull()]
            doc = pd.Series(pd.to_numeric(df[doc_no], downcast='integer').unique())
            gap = []
            doc = doc.sort_values().reset_index(drop = True)
            for i in range(len(doc)):
                if i == 0:
                    gap.append([doc[i],0])
                elif i > 0:
                    v = doc[i] - doc[i-1]
                    gap.append([doc[i],v])

            gaps = pd.DataFrame(gap,columns=[doc_no,"Gaps in entries"])

            gaps = gaps[gaps["Gaps in entries"]>0]
            
#             doc_no = doc_no.strip()        
#             df = dataset.copy()
#             df = df[df[doc_no].notnull()]
#             doc = pd.Series(df[doc_no].astype(int).unique())
#             gap = []
#             doc = doc.sort_values().reset_index(drop = True)
#             for i in range(len(doc)):
#                 if i == 0:
#                     gap.append(0)
#                 elif i > 0:
#                     v = doc[i] - doc[i-1]
#                     gap.append(v)
#             gap = pd.Series(gap)

#             gaps = pd.DataFrame(columns=[doc_no,"Difference"])
#             gaps[doc_no] = doc
#             gaps["Difference"] = gap

#             gaps = gaps[gaps["Difference"] > 1]
            
            if len(gaps) > 0 :
                gaps_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
                link[5] = f"Tab {num}"
                num += 1
                gaps_tab["A1"].value = client_name
                gaps_tab['A1'].font.bold = True
                gaps_tab["A2"].value = client_period
                gaps_tab['A2'].font.bold = True
                gaps_tab["A3"].value = "Gaps/jumps in Journal Entry numbers."
                gaps_tab['A3'].font.bold = True


                gaps_tab["A5"].value = "Objective: To find out gaps in Journal Entry / Document numbers."
                gaps_tab['A5'].font.bold = True

                gaps_tab["A7"].value = 'Method: All codes and reference test each reference  via formula to locate gaps in document sequence.'
                gaps_tab['A7'].font.bold = True


                r = gaps.shape[0] + 10
                c = gaps.shape[1]

                data_shape = f"A11:{letter[c]}{r}"
                c_shape = f"A10:{letter[c]}10"

                import datetime
                gaps = gaps.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                gaps_tab["A10"].options(pd.DataFrame, index=False).value = gaps
                gaps_tab.range(c_shape).font.bold = True
                gaps_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                gaps_tab.range(c_shape).columns.autofit()
                gaps_tab.range('A11').columns.autofit()
                gaps_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                gaps_tab.range(data_shape).api.Borders(2).LineStyle =  1 
                gaps_tab.range(data_shape).api.Borders(4).LineStyle =  1 
                gaps_tab.range(c_shape).api.Borders(3).LineStyle =  1 
                gaps_tab.range(c_shape).api.Borders(2).LineStyle =  1 
                
                gaps_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
                gaps_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            else:
                gaps = "Gaps not found"
        except Exception as e:
            print(e)
            gaps = "Something else went wrong"

    else:
        gaps = "Col name is not given"
    
    return gaps


# ### TEST 7

# In[10]:


def rev_debit(Account_codes, Amount):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if account_codes != "na" and amount != "na":
        
        account_codes = account_codes.strip()
        amount = amount.strip()
        df = dataset.copy()
        df = df[df[account_codes].notnull()]
#         df[account_codes] = df[account_codes].astype(int)
        df[amount] = df[amount].astype("float")
        try:
            df[account_codes] = pd.to_numeric(df[account_codes], downcast='integer')
        except:
            pass
        
        rev_df = df[df[account_codes].astype(str).str.strip().isin(rev_code) & (df[amount] > 0)]
        rev_df = rev_df.sort_values(amount, ascending=False)
        
        if len(rev_df) > 0:
            rev_df_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[6] = f"Tab {num}"
            num += 1
            rev_df_tab["A1"].value = client_name
            rev_df_tab['A1'].font.bold = True
            rev_df_tab["A2"].value = client_period
            rev_df_tab['A2'].font.bold = True
            rev_df_tab["A3"].value = "Summary of Debit transactions in Revenue codes."
            rev_df_tab['A3'].font.bold = True


            rev_df_tab["A5"].value = "Objective: To find out all debit entries in revenue codes."
            rev_df_tab['A5'].font.bold = True

            rev_df_tab["A7"].value = 'Method: Selected all revenue codes and filtered all debits entries.'
            rev_df_tab['A7'].font.bold = True


            r = rev_df.shape[0] + 10
            c = rev_df.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            rev_df = rev_df.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            rev_df_tab["A10"].options(pd.DataFrame, index=False).value = rev_df
            rev_df_tab.range(c_shape).font.bold = True
            rev_df_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            rev_df_tab.range(c_shape).columns.autofit()
            rev_df_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            rev_df_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            rev_df_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            rev_df_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            rev_df_tab.range(c_shape).api.Borders(2).LineStyle =  1 
            
            rev_df_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            rev_df_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            
            
            cell_no = r + 4
            
            if len(rev_df) > 0:
                rev_df_tab[f"A{cell_no}"].value = "Conclusion: Debit Entries found in Revenues Codes"
                rev_df_tab.range(f'A{cell_no}').api.Font.Bold = True
                rev_df_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


            else:
                rev_df_tab[f"A{cell_no}"].value = "Conclusion: No Debit Transactions in Revenue Codes"
                rev_df_tab.range(f'A{cell_no}').api.Font.Bold = True
                rev_df_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
        
        else:
            rev_df = "No Debit Transactions in Revenue Codes"

    else:
        rev_df = "Col Name is not given"
    return rev_df


# ### TEST 8, 9, 10

# In[11]:


def bank_pre(Bank_acc,vs_acc, col_1, col_2):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    try:
        # Step 1: Remove null values for account_codes and doc_no
        entries = dataset[dataset[account_codes].notnull()]
        entries = entries[entries[doc_no].notnull()]
        
        # Step 2: Convert account_codes and doc_no to consistent string format
        try:
            entries[account_codes] = pd.to_numeric(entries[account_codes], downcast="integer").astype(str)
        except:
            entries[account_codes] = entries[account_codes].astype(str)
        
        try:
            entries[doc_no] = pd.to_numeric(entries[doc_no], downcast="integer").astype(str)
        except:
            entries[doc_no] = entries[doc_no].astype(str)
        
        # Step 3: Filter entries for bank and vs accounts
        bank_entries = entries[entries[account_codes].str.strip().isin(bank_acc)]
        vs_entries = entries[entries[account_codes].str.strip().isin(vs_acc)]
        
        # Step 4: Get unique document numbers for both
        bank_doc_no = set(bank_entries[doc_no].unique())
        vs_doc_no = set(vs_entries[doc_no].unique())
        
        # Step 5: Find strictly matched document numbers
        matched_doc_no = bank_doc_no.intersection(vs_doc_no)
        
        # Step 6: Keep only matched records
        bank_acc_df = bank_entries[bank_entries[doc_no].isin(matched_doc_no)].reset_index(drop=True)
        vs_acc_df = vs_entries[vs_entries[doc_no].isin(matched_doc_no)].reset_index(drop=True)
        
        # Step 7: Create DataFrame with matched document numbers
        match_df = pd.DataFrame({col_1: list(matched_doc_no)})
        
        # Step 8: Concatenate only matched records
        concat_df = pd.concat([bank_acc_df, vs_acc_df], ignore_index=True)
        
        # Step 9: Merge to show matched document numbers in a single table
        match = pd.merge(
            pd.DataFrame(bank_acc_df[doc_no].unique(), columns=[col_1]),
            pd.DataFrame(vs_acc_df[doc_no].unique(), columns=[col_2]),
            left_on=col_1, right_on=col_2
        )
        
        # Step 10: Format output
        if bank_acc_df.shape[0] > vs_acc_df.shape[0]:
            df = pd.DataFrame()
            df[col_1] = sorted(bank_acc_df[doc_no].unique().astype(str))
            df[col_2] = sorted(vs_acc_df[doc_no].unique().astype(str))
        else:
            df = pd.DataFrame()
            df[col_2] = sorted(vs_acc_df[doc_no].unique().astype(str))
            df[col_1] = sorted(bank_acc_df[doc_no].unique().astype(str))
        
        # Step 11: Store matched document numbers
        match_acc = list(match[col_1])
        
        # Step 12: Filter only matched transactions
        if len(match_acc) > 0:         
            test = df
            test_record = concat_df[concat_df[doc_no].isin(match_acc)]
            test_record = test_record.sort_values(amount, ascending=False)
        else:
            test = df
            test_record = pd.DataFrame()


        


        test_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        if vs_acc == pre_acc:
            link[7] = f"Tab {num}"
        
        elif vs_acc == accrual_acc:
            link[8] = f"Tab {num}"
            
        elif vs_acc == pl_acc:
            link[9] = f"Tab {num}"
        

        n = num
        num += 1
        test_tab["A1"].value = client_name
        test_tab['A1'].font.bold = True
        test_tab["A2"].value = client_period
        test_tab['A2'].font.bold = True
        test_tab["A3"].value = f"{col_2.split()[0]} vs Bank."
        test_tab['A3'].font.bold = True


        test_tab["A5"].value = f"Objective: To find out contra entries of Bank Codes and {col_2.split()[0]} Codes."
        test_tab['A5'].font.bold = True

        test_tab["A7"].value = f'Method: Selected all voucher/journal numbers of Bank Codes and searched for similar voucher/journal numbers in {col_2.split()[0]} ledgers.'
        test_tab['A7'].font.bold = True

        test_tab["A9"].value = f"{col_2.split()[0]} Vs Bank."
        test_tab['A9'].font.bold = True
#         test_tab.range('A9').columns.autofit()

    #     test_tab["A10"].value = 'Accrual Vs Bank'
    #     test_tab['A10'].font.bold = True


        r = test.shape[0] + 12
        c = test.shape[1]

        data_shape = f"A13:{letter[c]}{r}"
        c_shape = f"A12:{letter[c]}12"

        import datetime
        # 255 , 200 , 255
        test = test.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
        test_tab["A12"].options(pd.DataFrame, index=False).value = test
        test_tab.range(c_shape).font.bold = True
        test_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
        test_tab.range(c_shape).columns.autofit()
        test_tab.range('A9').columns.autofit()
        test_tab.range(data_shape).api.Borders(3).LineStyle =  1 
        test_tab.range(data_shape).api.Borders(2).LineStyle =  1
        test_tab.range(data_shape).api.Borders(4).LineStyle =  1
        test_tab.range(c_shape).api.Borders(3).LineStyle =  1 
        test_tab.range(c_shape).api.Borders(2).LineStyle =  1 
        
        test_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        test_tab.range(f"A12:{letter[c]}{r}").columns.autofit()
        test_tab.range('A9').columns.autofit()
        
        
        cell_no = r+4
        if test_record.shape[0] > 0:
            test_tab[f"A{cell_no}"].value = f"Conclusion: Contra entries between Bank Codes and {col_2.split()[0]} Codes found."
            test_tab.range(f'A{cell_no}').api.Font.Bold = True
            test_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

        else:
            test_tab[f"A{cell_no}"].value = f"Conclusion: No Contra entries between Bank codes and {col_2.split()[0]} Codes found."
            test_tab.range(f'A{cell_no}').api.Font.Bold = True
            test_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
            


        if test_record.shape[0] > 0:
            r = test_record.shape[0]
            r = r+1
            c = test_record.shape[1]

            data_shape = f"A2:{letter[c]}{r}"
            c_shape = f"A1:{letter[c]}1"
            sht_name = f"Tab{n} Record"


            record_tab = wb.sheets.add(sht_name, after = wb.sheets.active)
            import datetime
            test_record = test_record.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            record_tab["A1"].options(pd.DataFrame, index=False).value = test_record
            record_tab.range(c_shape).font.bold = True
            record_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            record_tab.range(c_shape).columns.autofit()
            record_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            record_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            record_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            record_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            record_tab.range(c_shape).api.Borders(2).LineStyle =  1
            
            record_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            record_tab.range(f"A1:{letter[c]}{r}").columns.autofit()
            
            
            test_tab["B9"].value = f'=HYPERLINK("#\'{sht_name}\'!A1","{sht_name}")'
            return test, test_record
        else:
            return test
    except Exception as e:
        print(e)
        print("Something else went wrong")


# ### TEST 11

# In[12]:


def directors(col):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    
    try:
        
        if col != "na":
            col = col.strip()        
            df = dataset.copy()
            df[col] = df[col].astype(str).str.strip()
            director = df[col].value_counts()
            df_director = pd.DataFrame()
            df_director["Posted By"] =  director.index
            df_director["No."] =  director.values

            df_director_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[10] = f"Tab {num}"
            num += 1
            df_director_tab["A1"].value = client_name
            df_director_tab['A1'].font.bold = True
            df_director_tab["A2"].value = client_period
            df_director_tab['A2'].font.bold = True
            df_director_tab["A3"].value = "Postings by directors on Companies house."
            df_director_tab['A3'].font.bold = True

            df_director_tab["A5"].value = "Objective: To Find posting by directors on Company House."
            df_director_tab['A5'].font.bold = True

            df_director_tab["A7"].value = "Method: Count all posting by users on Company House."
            df_director_tab['A7'].font.bold = True

            r = df_director.shape[0] + 10
            c = df_director.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            df_director = df_director.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            df_director_tab["A10"].options(pd.DataFrame, index=False).value = df_director
            df_director_tab.range(c_shape).font.bold = True
            df_director_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            df_director_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            df_director_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            df_director_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            df_director_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            df_director_tab.range(c_shape).api.Borders(2).LineStyle =  1
            
            df_director_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            df_director_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            

        else:
            df_director = "Col name is not given"
    
    except Exception as e:
        print(e)
        print("Something else went wrong")  
    
    return df_director


# In[1]:

def dup_entry():
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global client_name,client_period,link
    global df, dataset
    
    df = dataset.copy()
    
    temp =  df[df.duplicated(keep=False)]
    temp = temp.sort_values(amount, ascending=False)
    
    if len(temp) == 0:
        return 'No dup entry'
    
    else:
        link.append("-")
        df_dup_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[11] = f"Tab {num}"
        num += 1
        df_dup_tab["A1"].value = client_name
        df_dup_tab['A1'].font.bold = True
        df_dup_tab["A2"].value = client_period
        df_dup_tab['A2'].font.bold = True
        df_dup_tab["A3"].value = "Possible duplicate Journal entries."
        df_dup_tab['A3'].font.bold = True

        df_dup_tab["A5"].value = "Objective: To Find possible duplicate Journal entries."
        df_dup_tab['A5'].font.bold = True

        df_dup_tab["A7"].value = "Method: Selected all transactions which are similar to eachother."
        df_dup_tab['A7'].font.bold = True

        r = temp.shape[0] + 10
        c = temp.shape[1]

        data_shape = f"A11:{letter[c]}{r}"
        c_shape = f"A10:{letter[c]}10"

        import datetime
        temp = temp.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
        df_dup_tab["A10"].options(pd.DataFrame, index=False).value = temp
        df_dup_tab.range(c_shape).font.bold = True
        df_dup_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
        df_dup_tab.range(data_shape).api.Borders(3).LineStyle =  1 
        df_dup_tab.range(data_shape).api.Borders(2).LineStyle =  1 
        df_dup_tab.range(data_shape).api.Borders(4).LineStyle =  1 
        df_dup_tab.range(c_shape).api.Borders(3).LineStyle =  1 
        df_dup_tab.range(c_shape).api.Borders(2).LineStyle =  1

        df_dup_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        df_dup_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
        
        df_dup_tab[f"A{r+4}"].value = f"Conclusion: {len(temp)} duplicate entries found."
        df_dup_tab.range(f'A{r+4}').api.Font.Bold = True
        df_dup_tab.range(f'A{r+4}').font.name = 'Times New Roman'

### #Test 12

def fraud_word_check(acc_description):
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset
    
    try:
        if acc_description != "na":
            acc_description = acc_description.strip()
            df = dataset.copy()

            # Load fraud keywords
            try:
                key = pd.read_excel("Keywords.xlsx")
                fraud_keywords = key["Words"].dropna().str.strip().tolist()
            except:
                print("Error: Keywords.xlsx file not found")
                return "Keywords file not found"

            # Search for fraud keywords in description
            fraud_entries = pd.DataFrame()
            for keyword in fraud_keywords:
                pattern = rf"\b{keyword}\b"  # match whole words
                temp = df[df[acc_description].str.contains(pattern, na=False, case=False, regex=True)]
                if not temp.empty:
                    temp = temp.copy()
                    temp["Keyword"] = keyword
                    fraud_entries = pd.concat([fraud_entries, temp], ignore_index=True)

            # Create results tab
            # Ensure link list has enough elements
            while len(link) <= 12:
                link.append("-")
            fraud_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[12] = f"Tab {num}"
            num += 1
            # fraud_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            # link.append(f"Tab {num}")
            # num += 1

            fraud_tab["A1"].value = client_name
            fraud_tab['A1'].font.bold = True
            fraud_tab["A2"].value = client_period
            fraud_tab['A2'].font.bold = True
            fraud_tab["A3"].value = "Fraud Word Testing"
            fraud_tab['A3'].font.bold = True

            if not fraud_entries.empty:
                # Write fraud matches to Excel
                r = fraud_entries.shape[0] + 7  # headers at row 7
                c = fraud_entries.shape[1]

                data_shape = f"A8:{letter[c]}{r}"
                c_shape = f"A7:{letter[c]}7"

                import datetime
                fraud_entries = fraud_entries.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                fraud_tab["A7"].options(pd.DataFrame, index=False).value = fraud_entries
                fraud_tab.range(c_shape).font.bold = True
                fraud_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

                # Borders
                fraud_tab.range(data_shape).api.Borders(3).LineStyle = 1 
                fraud_tab.range(data_shape).api.Borders(2).LineStyle = 1 
                fraud_tab.range(data_shape).api.Borders(4).LineStyle = 1 
                fraud_tab.range(c_shape).api.Borders(3).LineStyle = 1 
                fraud_tab.range(c_shape).api.Borders(2).LineStyle = 1

                fraud_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
                fraud_tab.range(f"A7:{letter[c]}{r}").columns.autofit()

                fraud_tab[f"A{r+2}"].value = f"Conclusion: {len(fraud_entries)} suspicious entries found."
                fraud_tab[f"A{r+2}"].font.bold = True

            else:
                # No matches found
                fraud_tab["A5"].value = "Objective: To identify potential fraud-related transactions by searching for suspicious keywords."
                fraud_tab["A5"].font.bold = True
                fraud_tab["A7"].value = "Method: Searched all journal descriptions for predefined fraud-related keywords."
                fraud_tab["A7"].font.bold = True
                fraud_tab["A9"].value = "Conclusion: No entries with fraud-related keywords found."
                fraud_tab["A9"].font.bold = True

                fraud_tab.range("A1:A9").font.name = 'Times New Roman'
                fraud_tab.range("A1:A9").columns.autofit()

            return fraud_entries if not fraud_entries.empty else "No fraud entries found"
        
        else:
            return "Column name not provided"

    except Exception as e:
        print(f"Error in fraud_word_check: {e}")
        return "Error processing fraud word check"


#### ##Test 13

def sales_chronological(account_codes, rev_code, doc_no):
    global wb, sheet_name, letter, n, num
    global date
    global client_name, client_period, link
    global df, dataset
    
    try:
        df = dataset.copy()

        # Filter revenue accounts only
        df = df[df[account_codes].astype(str).isin([str(x) for x in rev_code])]
        if df.empty:
            return "No revenue account entries found"

        # Convert Date column
        df[date] = pd.to_datetime(df[date], errors="coerce")

        # Sort by Document Number
        df_sorted = df.sort_values(by=doc_no).reset_index(drop=True)

        # Check chronological order for every row
        df_sorted["Chronological Order"] = df_sorted[date].diff().dt.days >= 0
        df_sorted.loc[0, "Chronological Order"] = True  # First row always valid

        # Create results tab
        # Ensure link list has enough elements
        while len(link) <= 13:
            link.append("-")
        chrono_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[13] = f"Tab {num}"
        num += 1
        

        # Header info
        chrono_tab["A1"].value = client_name
        chrono_tab['A1'].font.bold = True
        chrono_tab["A2"].value = client_period
        chrono_tab['A2'].font.bold = True
        chrono_tab["A3"].value = "Chronological Test for Sales Codes"
        chrono_tab['A3'].font.bold = True

        # Objective & Method
        chrono_tab["A5"].value = "Objective: To find out Chronological Order for Sales Code."
        chrono_tab['A5'].font.bold = True
        chrono_tab["A8"].value = 'Note: False In "Chronological Order" shows the Chronological Violation.'
        chrono_tab['A8'].font.bold = True

        # Write full dataset with chronological check
        r = df_sorted.shape[0] + 12
        c = df_sorted.shape[1]
        data_shape = f"A13:{letter[c]}{r}"   # data rows
        c_shape = f"A12:{letter[c]}12"      # header row

        chrono_tab["A12"].options(pd.DataFrame, index=False).value = df_sorted
        chrono_tab.range(c_shape).font.bold = True
        chrono_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

        # Borders
        chrono_tab.range(data_shape).api.Borders(3).LineStyle = 1 
        chrono_tab.range(data_shape).api.Borders(2).LineStyle = 1 
        chrono_tab.range(data_shape).api.Borders(4).LineStyle = 1 
        chrono_tab.range(c_shape).api.Borders(3).LineStyle = 1 
        chrono_tab.range(c_shape).api.Borders(2).LineStyle = 1

        # Formatting
        chrono_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        chrono_tab.range(f"A12:{letter[c]}{r}").columns.autofit()

        # Conclusion
        violations = df_sorted[df_sorted["Chronological Order"] == False]
        if not violations.empty:
            chrono_tab[f"A{r+2}"].value = f"Conclusion: {len(violations)} out-of-order entries found."
        else:
            chrono_tab[f"A{r+2}"].value = "Conclusion: All revenue entries follow chronological order."
        chrono_tab[f"A{r+2}"].font.bold = True

        return df_sorted

    except Exception as e:
        print(f"Error in sales_chronological: {e}")
        return "Error processing sales chronological test"

### ##TEST14

def credits_in_revenue(account_codes, rev_code, doc_no):
    global wb, sheet_name, letter, n, num
    global client_name, client_period, link
    global df, dataset
    
    try:
        df = dataset.copy()

        # Step 1: Identify all Document Numbers linked to revenue accounts
        revenue_docs = df[df[account_codes].astype(str).isin([str(x) for x in rev_code])][doc_no].unique().tolist()
        if not revenue_docs:
            return "No revenue-related documents found"

        # Step 2: Extract all entries with those Document Numbers
        revenue_related = df[df[doc_no].isin(revenue_docs)].reset_index(drop=True)

        # Step 3: Create results tab
        # Ensure link list has enough elements
        while len(link) <= 14:
            link.append("-")
        credits_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[14] = f"Tab {num}"
        num += 1

        # Header info
        credits_tab["A1"].value = client_name
        credits_tab['A1'].font.bold = True
        credits_tab["A2"].value = client_period
        credits_tab['A2'].font.bold = True
        credits_tab["A3"].value = "Revenue entires with dr posted to GLs."
        credits_tab['A3'].font.bold = True

        # Step 4: Write table at A7
        r = revenue_related.shape[0] + 7
        c = revenue_related.shape[1]
        data_shape = f"A8:{letter[c]}{r}"   # data rows
        c_shape = f"A7:{letter[c]}7"        # header row

        credits_tab["A7"].options(pd.DataFrame, index=False).value = revenue_related
        credits_tab.range(c_shape).font.bold = True
        credits_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

        # Borders
        credits_tab.range(data_shape).api.Borders(3).LineStyle = 1 
        credits_tab.range(data_shape).api.Borders(2).LineStyle = 1 
        credits_tab.range(data_shape).api.Borders(4).LineStyle = 1 
        credits_tab.range(c_shape).api.Borders(3).LineStyle = 1 
        credits_tab.range(c_shape).api.Borders(2).LineStyle = 1

        # Formatting
        credits_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        credits_tab.range(f"A7:{letter[c]}{r}").columns.autofit()

        # Step 5: Conclusion
        credits_tab[f"A{r+2}"].value = f"Conclusion: {len(revenue_related)} entries found linked to revenue documents."
        credits_tab[f"A{r+2}"].font.bold = True

        return revenue_related

    except Exception as e:
        print(f"Error in credits_in_revenue: {e}")
        return "Error processing credits in revenue test"


    
def jt():
    global wb, sheet_name, letter, n, num, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global entry_file, df, dataset, monthly_tab, value, note_tracker

    sheet_name = entry_file.get()

    if sheet_name == "":
        messagebox.showinfo(title="Error", message="Entry Fields can't be empty")
        return

    if sheet_name not in os.listdir():
        messagebox.showinfo(title="Error", message="Enter Correct Sheet Name")
        return

    try:
        # Initialize variables
        wb = xw.Book()
        letter = {i: chr(64 + i) if i <= 26 else f"A{chr(64 + i - 26)}" for i in range(1, 100)}
        num = 1
        link = []
        note_tracker = {}

        # Load data
        try:
            df = pd.read_excel(sheet_name)
        except:
            df = pd.read_csv(sheet_name)

        dataset = df.copy()

        # Load configuration data
        config_file = "SOP for Data Analyst.xlsx"

        rev_code = list(pd.read_excel(config_file, sheet_name="Revenue Code")["Revenue Code"].astype(str))
        bank_acc = list(pd.read_excel(config_file, sheet_name="Bank Code")["Bank Code"].astype(str))
        pre_acc = list(pd.read_excel(config_file, sheet_name="Prepayment Code")["Prepayment Code"].astype(str))
        accrual_acc = list(pd.read_excel(config_file, sheet_name="Accrual Code")["Accrual Code"].astype(str))
        pl_acc = list(pd.read_excel(config_file, sheet_name="PL Code")["PL Code"].astype(str))

        # Load column mappings
        col_name = pd.read_excel(config_file, sheet_name="Col Name").iloc[:, 1]
        account_codes, doc_no, date, amount, acc_description = col_name[0:5]
        acc_type, time, post_by, date_format, time_format = col_name[5:10]

        # Load client details
        client_detail = pd.read_excel(config_file, sheet_name="Journal Testing", header=None)
        client_name = client_detail.iloc[1, 0]
        client_period = f"Period : {client_detail.iloc[8, 1].strftime('%B %Y')} To {client_detail.iloc[8, 2].strftime('%B %Y')}"

        # Load holiday dates
        holiday_detail = pd.read_excel(config_file, sheet_name="Journal Testing", header=None)
        date_index = holiday_detail[holiday_detail[0].str.strip() == "Date"].index

        holiday_dates = []
        if len(date_index) > 0:
            for date_val in holiday_detail.iloc[date_index[0]+1:, 0].values:
                if pd.isna(date_val):
                    break
                holiday_dates.append(date_val.strftime("%Y-%m-%d"))

        # Create summary sheet
        summary_sheet()

        # Run all tests
        monthly_tab = ""
        value = ""

        round_entries(amount)
        holidaysandweekend(date, date_format)
        odd_hours_entries(time, time_format)
        test_4 = transactions_per_month(date, date_format)
        reversed_entries(acc_description)
        gaps(doc_no)
        rev_debit(account_codes, amount)

        # Bank comparison tests
        bank_pre(bank_acc, pre_acc, f'Bank {doc_no}', f'Prepayment {doc_no}')
        bank_pre(bank_acc, accrual_acc, f'Bank {doc_no}', f'Accrual {doc_no}')
        bank_pre(bank_acc, pl_acc, f'Bank {doc_no}', f'PL {doc_no}')

        # Additional tests
        directors(post_by)
        dup_entry()
        fraud_word_check(acc_description)
        sales_chronological(account_codes, rev_code, doc_no)
        credits_in_revenue(account_codes, rev_code, doc_no)

        # Extract client name and create proper filename
        client_parts = client_name.split(":")
        if len(client_parts) > 1:
            clean_client_name = client_parts[1].strip()
        else:
            clean_client_name = client_name.strip()

        # Remove invalid filename characters
        clean_client_name = re.sub(r'[<>:"/\\|?*]', '', clean_client_name)
        final_filename = f"{clean_client_name} - Journal Testing"

        file_name = wb.name
        wb.save()
        wb.close()

        # Create charts if monthly data exists
        if monthly_tab and value is not None and not value.empty:
            l = len(value.iloc[:, 0])
            wb_openpyxl = openpyxl.load_workbook(f'{file_name}.xlsx')
            sheet = wb_openpyxl[monthly_tab]

            # Chart 1: Transaction percentages comparison
            values = Reference(sheet, min_col=4, max_col=4, min_row=9, max_row=9+l)
            values1 = Reference(sheet, min_col=5, max_col=5, min_row=9, max_row=9+l)
            cats = Reference(sheet, min_col=1, max_col=1, min_row=10, max_row=10+l)

            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.add_data(values1, titles_from_data=True)
            chart.set_categories(cats)
            chart.title = "No. of Trans% VS Value of Trans%"
            sheet.add_chart(chart, "G9")

            # Chart 2: Number of transactions
            values = Reference(sheet, min_col=2, max_col=2, min_row=9, max_row=9+l)
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            chart.title = "No. Of Transaction"
            sheet.add_chart(chart, "P9")

            # Chart 3: Value of transactions
            values = Reference(sheet, min_col=3, max_col=3, min_row=9, max_row=9+l)
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            chart.title = "Value Of Transaction"
            sheet.add_chart(chart, "Y9")

            # Add hyperlinks to summary sheet
            thin_border = Border(
                left=Side(border_style=BORDER_THIN, color='00000000'),
                right=Side(border_style=BORDER_THIN, color='00000000'),
                top=Side(border_style=BORDER_THIN, color='00000000'),
                bottom=Side(border_style=BORDER_THIN, color='00000000')
            )

            sheet = wb_openpyxl["Summary"]
            n = 5
            for i in link:
                n += 1
                if i != "-":
                    sheet[f"B{n}"] = f'=HYPERLINK("#\'{i}\'!A1","{i}")'
                    sheet[f"B{n}"].style = "Hyperlink"
                    sheet.cell(row=n, column=2).border = thin_border

            wb_openpyxl.save(f"{final_filename}.xlsx")
            wb_openpyxl.close()
        else:
            # Just rename the file if no charts needed
            wb_openpyxl = openpyxl.load_workbook(f'{file_name}.xlsx')
            wb_openpyxl.save(f"{final_filename}.xlsx")
            wb_openpyxl.close()

        # Remove temporary file if it exists
        if os.path.exists(f'{file_name}.xlsx') and file_name != final_filename:
            os.remove(f'{file_name}.xlsx')

            messagebox.showinfo(title="Done", message=f"File saved as: {final_filename}.xlsx")
            
        except Exception as e:
            for i in os.listdir():
                if i.startswith("Book"):
                    os.remove(i)
            print(e)
            messagebox.showinfo(title = "Error", message = "Something went wrong")


# In[14]:


# Global variables
sheet_name = ""
wb = ""
letter = ""
n = 1
num = 1
rev_code, bank_acc, pre_acc, accrual_acc, pl_acc = "", "", "", "", ""
account_codes, doc_no, date, amount, acc_description = "", "", "", "", ""
acc_type, time, post_by, date_format, time_format = '', "", "", "", ""
client_name, client_period, holiday_dates, link = "", "", "", ""
df, dataset = "", ""
monthly_tab, value = "", ""
note_tracker = {}  # Track which notes are added


# In[15]:


window = Tk()
window.title('JT')

app_width = 550
app_height = 650

style = Style()
style.configure('W.TButton', font =  ('calibri', 14, 'bold'),foreground = 'Blue',background='Blue')

window.geometry(f'{app_width}x{app_height}+380+20')
window.resizable(False,False)

# title = Label(window,text = "PTIS", font = ("Arial Bold",30)).place(x=300,y=5)
title = Label(window,text = "Journal Testing",font = ("Arial Bold",20),foreground='red').place(x=170,y=10)

test1 = Label(window,text = "Test 1:     Round entries ,000 or ,999",font = ("calibri",12))
test2 = Label(window,text = "Test 2:     Date of postings: weekends, bank holidays etc.",font = ("calibri",12))
test3 = Label(window,text = "Test 3:     Timings of postings - any postings on odd hours.",font = ("calibri",12))
test4 = Label(window,text = "Test 4:     Total amount of transactions per month",font = ("calibri",12))
test5 = Label(window,text = "Test 5:     Reversed Journal Entries",font = ("calibri",12))
test6 = Label(window,text = "Test 6:     Gaps/jumps in Journal Entry numbers",font = ("calibri",12))
test7 = Label(window,text = "Test 7:     Summary of Debit transactions in Revenue codes",font = ("calibri",12))
test8 = Label(window,text = "Test 8:     Prepayments vs Bank",font = ("calibri",12))
test9 = Label(window,text = "Test 9:     Accruals vs Bank",font = ("calibri",12))
test10 = Label(window,text = "Test 10:   Bank accounts vs PnL accounts.",font = ("calibri",12))
test11 = Label(window,text = "Test 11:   Postings by directors on Companies house.",font = ("calibri",12))
test12 = Label(window,text = "Test 12:   Possible duplicate Journal entries.",font = ("calibri",12))
test13 = Label(window,text = "Test 13:   Fraud Word Check.",font = ("calibri",12))
test14 = Label(window,text = "Test 14:   Sales Chronological Testing.",font = ("calibri",12))
test15 = Label(window,text = "Test 15:   Credits in Revenue.",font = ("calibri",12))



label_file = Label(window,text = "File Name: ",font = ("calibri",16),foreground="Red")
entry_file = Entry(window,width = 26,font=('Arial 12'))
bt = Button(window,text = "Test All",style='W.TButton',command = jt)

pos = 90
gap = 30
test1.place(x= 20, y= pos)
pos += gap
test2.place(x= 20, y= pos)
pos += gap
test3.place(x= 20, y= pos)
pos += gap
test4.place(x= 20, y= pos)
pos += gap
test5.place(x= 20, y= pos)
pos += gap
test6.place(x= 20, y= pos)
pos += gap
test7.place(x= 20, y= pos)
pos += gap
test8.place(x= 20, y= pos)
pos += gap
test9.place(x= 20, y= pos)
pos += gap
test10.place(x= 20, y= pos)
pos += gap
test11.place(x= 20, y= pos)
pos += gap
test12.place(x= 20, y= pos)
pos += gap
test13.place(x= 20, y= pos)
pos += gap
test14.place(x= 20, y= pos)
pos += gap
test15.place(x= 20, y= pos)

pos += 60
label_file.place(x=90,y=pos-3)
entry_file.place(x=195,y=pos)

pos += 40
bt.place(x= 215, y= pos)

# wb = xw.Book()
# letter = {1: 'A', 2: 'B', 3: 'C', 4: 'D', 5: 'E', 6: 'F', 7: 'G', 8: 'H', 9: 'I', 10: 'J', 11: 'K', 12: 'L', 13: 'M', 14: 'N', 15: 'O', 16: 'P', 17: 'Q', 18: 'R', 19: 'S', 20: 'T', 21: 'U', 22: 'V', 23: 'W', 24: 'X', 25: 'Y', 26: 'Z', 27: 'AA', 28: 'AB', 29: 'AC', 30: 'AD', 31: 'AE', 32: 'AF', 33: 'AG', 34: 'AH', 35: 'AI', 36: 'AJ', 37: 'AK', 38: 'AL', 39: 'AM', 40: 'AN', 41: 'AO', 42: 'AP', 43: 'AQ', 44: 'AR', 45: 'AS', 46: 'AT', 47: 'AU', 48: 'AV', 49: 'AW', 50: 'AX', 51: 'AY', 52: 'AZ', 53: 'BA', 54: 'BB', 55: 'BC', 56: 'BD', 57: 'BE', 58: 'BF', 59: 'BG', 60: 'BH', 61: 'BI', 62: 'BJ', 63: 'BK', 64: 'BL', 65: 'BM', 66: 'BN', 67: 'BO', 68: 'BP', 69: 'BQ', 70: 'BR', 71: 'BS', 72: 'BT', 73: 'BU', 74: 'BV', 75: 'BW', 76: 'BX', 77: 'BY', 78: 'BZ', 79: 'CA', 80: 'CB', 81: 'CC', 82: 'CD', 83: 'CE', 84: 'CF', 85: 'CG', 86: 'CH', 87: 'CI', 88: 'CJ', 89: 'CK', 90: 'CL', 91: 'CM', 92: 'CN', 93: 'CO', 94: 'CP', 95: 'CQ', 96: 'CR', 97: 'CS', 98: 'CT', 99: 'CU'}

window.mainloop()


